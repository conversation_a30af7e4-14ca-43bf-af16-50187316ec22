import requests
import json
from datetime import datetime
import time

class WorkloadCheckAnalyzer:
    def __init__(self):
        self.base_url = "https://bss.phone580.com/fzs-outgateway-admin/hive/log/list"
        self.headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json',
            'origin': 'https://bss.phone580.com',
            'priority': 'u=1, i',
            'referer': 'https://bss.phone580.com/bss-admin/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0',
            'x-requested-with': 'XMLHttpRequest',
            # 注意：这里的Cookie需要您提供最新的
            'Cookie': '_ga=GA1.2.1710210320.1727571063; gdp_user_id=gioenc-g1dg1d6g%2C673d%2C5b71%2Cce56%2C637741767e92; cna=f9bb870b0bc04fb38eaabf51f03690db; 8d2279a5e2f18b7c_gdp_session_id_sent=ab5b45cb-2ef3-4604-a5ef-d2537164d294; 8d2279a5e2f18b7c_gdp_sequence_ids={%22globalKey%22:115%2C%22VISIT%22:9%2C%22PAGE%22:23%2C%22CUSTOM%22:46%2C%22VIEW_CHANGE%22:17%2C%22VIEW_CLICK%22:24}; SESSION_ID=28A2AE647A7AE7E1449B2D821071C599; APP_ID=49; authToken=43d620f041521d13f1d7555ec8b6cfc6; authTokens=43d620f041521d13f1d7555ec8b6cfc6; JSESSIONID=17AFFA366C8ECD0396B5C6914CF94947; userName=%22%E6%9D%8E%E6%B0%B8%E6%9D%B0%22; SERVERID=c475eff2b2702eb905307c9f94bfbce0|1754380367|1754378239; APP_ID=49; SESSION_ID=28A2AE647A7AE7E1449B2D821071C599'
        }
        
    def get_page_data(self, page=1, page_size=500):
        """获取指定页的数据"""
        payload = {
            "interfaceCode": "sf2.workloadcheck.query",
            "status": "1",  # 这里查询所有状态为1的记录
            "page": page,
            "number": page_size,
            "beginTime": "2025-08-01 00:00:00",
            "endTime": "2025-08-05 23:59:59"
        }
        
        try:
            response = requests.post(self.base_url, headers=self.headers, json=payload)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求第{page}页时出错: {e}")
            return None
    
    def parse_result_status(self, result_str):
        """解析result字段中的状态"""
        try:
            result_data = json.loads(result_str)
            body_data = json.loads(result_data.get('body', '{}'))
            # 从body中获取data字段，false表示认证失败，true表示认证成功
            return body_data.get('data', None)
        except (json.JSONDecodeError, KeyError) as e:
            print(f"解析result字段出错: {e}")
            return None
    
    def extract_id_number(self, input_param_str):
        """从inputParam中提取idNumber"""
        try:
            input_data = json.loads(input_param_str)
            body_str = input_data.get('body', '{}')
            body_data = json.loads(body_str)
            return body_data.get('idNumber', None)
        except (json.JSONDecodeError, KeyError) as e:
            print(f"解析inputParam字段出错: {e}")
            return None

    def analyze_workload_check(self):
        """分析180天跑单校验结果"""
        print("开始分析180天跑单校验数据...")

        all_records = []
        page = 1
        page_size = 500

        while True:
            print(f"正在获取第{page}页数据...")
            data = self.get_page_data(page, page_size)

            if not data or data.get('resultCode') != '0000':
                print(f"获取第{page}页数据失败或无数据")
                break

            records = data.get('data', [])
            if not records:
                print(f"第{page}页无数据，结束获取")
                break

            all_records.extend(records)
            print(f"第{page}页获取到{len(records)}条记录")

            # 如果返回的记录数少于页面大小，说明已经是最后一页
            if len(records) < page_size:
                print("已获取所有数据")
                break

            page += 1
            time.sleep(0.5)  # 避免请求过快

        print(f"\n总共获取到{len(all_records)}条记录")

        # 分析数据
        failed_records = []  # 失败的记录
        failed_id_numbers = set()  # 失败的身份证号（去重）

        for record in all_records:
            result_str = record.get('result', '')
            auth_result = self.parse_result_status(result_str)

            # data为false表示认证失败
            if auth_result is False:
                failed_records.append(record)
                # 从inputParam中提取idNumber
                input_param = record.get('inputParam', '')
                id_number = self.extract_id_number(input_param)
                if id_number:
                    failed_id_numbers.add(id_number)

        # 输出统计结果
        print("\n=== 180天跑单校验失败统计 ===")
        print(f"总失败记录数: {len(failed_records)}")
        print(f"失败人数（按idNumber去重）: {len(failed_id_numbers)}")

        # 详细信息
        if failed_records:
            print(f"\n失败记录详情（前10条）:")
            for i, record in enumerate(failed_records[:10]):
                input_param = record.get('inputParam', '')
                id_number = self.extract_id_number(input_param)
                print(f"{i+1}. idNumber: {id_number}")
                print(f"   interfaceId: {record.get('interfaceId')}")
                print(f"   创建时间: {record.get('createTime')}")
                print(f"   接口名称: {record.get('interfaceName')}")
                print("   ---")

        return {
            'total_failed_records': len(failed_records),
            'total_failed_people': len(failed_id_numbers),
            'failed_records': failed_records,
            'failed_id_numbers': list(failed_id_numbers)
        }

def main():
    analyzer = WorkloadCheckAnalyzer()
    
    print("注意：请确保Cookie中的认证信息是最新的！")
    print("如果遇到认证失败，请更新headers中的Cookie字段\n")
    
    try:
        result = analyzer.analyze_workload_check()
        
        # 保存结果到文件
        with open('workload_check_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n结果已保存到 workload_check_result.json")
        
    except Exception as e:
        print(f"分析过程中出错: {e}")

if __name__ == "__main__":
    main()
